const logger = require('../config/logger');
const config = require('../config/config');

/**
 * Tyk reverse proxy logging middleware
 * Logs analytics and rate limiting information when requests come through Tyk
 */

/**
 * Extract Tyk-specific headers from request
 * @param {Object} req - Express request object
 * @returns {Object} - Tyk headers object
 */
const extractTykHeaders = (req) => {
  const tykHeaders = {};
  
  // Extract all headers that start with 'x-tyk-' or 'x-ratelimit-'
  Object.keys(req.headers).forEach(header => {
    if (header.startsWith('x-tyk-') || header.startsWith('x-ratelimit-')) {
      tykHeaders[header] = req.headers[header];
    }
  });

  return tykHeaders;
};

/**
 * Tyk request logging middleware
 * Logs incoming requests with Tyk-specific information
 */
const tykRequestLogger = (req, res, next) => {
  // Only log Tyk information if Tyk is enabled
  if (!config.tyk.enabled) {
    return next();
  }

  const startTime = Date.now();
  const tykHeaders = extractTykHeaders(req);
  
  // Store start time for response logging
  req.tykLogStartTime = startTime;
  
  // Log request with Tyk information
  logger.info('Tyk Request', {
    method: req.method,
    url: req.url,
    path: req.path,
    query: req.query,
    userAgent: req.headers['user-agent'],
    ip: req.ip,
    tykHeaders,
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'] || req.headers['x-tyk-request-id']
  });

  next();
};

/**
 * Tyk response logging middleware
 * Logs outgoing responses with Tyk-specific information
 */
const tykResponseLogger = (req, res, next) => {
  // Only log Tyk information if Tyk is enabled
  if (!config.tyk.enabled) {
    return next();
  }

  // Capture the original res.end function
  const originalEnd = res.end;
  
  res.end = function(chunk, encoding) {
    // Calculate response time
    const responseTime = req.tykLogStartTime ? Date.now() - req.tykLogStartTime : 0;
    const tykHeaders = extractTykHeaders(req);
    
    // Extract response headers that might be added by Tyk
    const responseHeaders = {};
    Object.keys(res.getHeaders()).forEach(header => {
      if (header.startsWith('x-tyk-') || header.startsWith('x-ratelimit-')) {
        responseHeaders[header] = res.getHeader(header);
      }
    });

    // Log response with Tyk information
    logger.info('Tyk Response', {
      method: req.method,
      url: req.url,
      path: req.path,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      contentLength: res.getHeader('content-length'),
      tykRequestHeaders: tykHeaders,
      tykResponseHeaders: responseHeaders,
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] || req.headers['x-tyk-request-id'],
      identity: req.identity ? {
        id: req.identity.identity_id,
        email: req.identity.email,
        authMethod: req.identity.auth_method
      } : null
    });

    // Call the original res.end function
    originalEnd.call(this, chunk, encoding);
  };

  next();
};

/**
 * Tyk error logging middleware
 * Logs errors with Tyk-specific context
 */
const tykErrorLogger = (err, req, res, next) => {
  // Only log Tyk information if Tyk is enabled
  if (!config.tyk.enabled) {
    return next(err);
  }

  const tykHeaders = extractTykHeaders(req);
  
  logger.error('Tyk Error', {
    error: {
      message: err.message,
      stack: err.stack,
      name: err.name,
      statusCode: err.statusCode
    },
    request: {
      method: req.method,
      url: req.url,
      path: req.path,
      query: req.query,
      userAgent: req.headers['user-agent'],
      ip: req.ip
    },
    tykHeaders,
    identity: req.identity ? {
      id: req.identity.identity_id,
      email: req.identity.email,
      authMethod: req.identity.auth_method
    } : null,
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'] || req.headers['x-tyk-request-id']
  });

  next(err);
};

/**
 * Tyk authentication logging middleware
 * Logs authentication events with Tyk-specific information
 */
const tykAuthLogger = (req, res, next) => {
  // Only log if Tyk is enabled and this is authentication via Tyk proxy
  if (!config.tyk.enabled || !req.identity?.auth_method?.includes('tyk')) {
    return next();
  }

  const tykHeaders = extractTykHeaders(req);
  
  logger.info('Tyk Authentication Event', {
    event: 'authentication_success',
    identity: {
      id: req.identity.identity_id,
      email: req.identity.email,
      authMethod: req.identity.auth_method,
      permissions: req.identity.permissions
    },
    tykHeaders,
    tykMetadata: req.identity.tyk_metadata,
    request: {
      method: req.method,
      url: req.url,
      path: req.path,
      userAgent: req.headers['user-agent'],
      ip: req.ip
    },
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'] || req.headers['x-tyk-request-id']
  });

  next();
};

/**
 * Tyk rate limit logging middleware
 * Logs rate limit information from Tyk headers
 */
const tykRateLimitLogger = (req, res, next) => {
  // Only log if Tyk is enabled
  if (!config.tyk.enabled) {
    return next();
  }

  const rateLimitHeaders = {
    remaining: req.headers['x-ratelimit-remaining'],
    limit: req.headers['x-ratelimit-limit'],
    reset: req.headers['x-ratelimit-reset']
  };

  // Only log if rate limit headers are present
  if (rateLimitHeaders.remaining || rateLimitHeaders.limit) {
    logger.info('Tyk Rate Limit Info', {
      rateLimit: rateLimitHeaders,
      identity: req.identity ? {
        id: req.identity.identity_id,
        email: req.identity.email
      } : null,
      request: {
        method: req.method,
        path: req.path,
        ip: req.ip
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] || req.headers['x-tyk-request-id']
    });

    // Warn if rate limit is getting low
    const remaining = parseInt(rateLimitHeaders.remaining, 10);
    const limit = parseInt(rateLimitHeaders.limit, 10);
    
    if (!isNaN(remaining) && !isNaN(limit) && remaining < limit * 0.1) {
      logger.warn('Tyk Rate Limit Warning', {
        message: 'Rate limit is running low',
        remaining,
        limit,
        percentage: Math.round((remaining / limit) * 100),
        identity: req.identity?.identity_id,
        timestamp: new Date().toISOString()
      });
    }
  }

  next();
};

/**
 * Combined Tyk logging middleware
 * Applies all Tyk logging middlewares in the correct order
 */
const tykLogging = [
  tykRequestLogger,
  tykResponseLogger,
  tykAuthLogger,
  tykRateLimitLogger
];

module.exports = {
  tykRequestLogger,
  tykResponseLogger,
  tykErrorLogger,
  tykAuthLogger,
  tykRateLimitLogger,
  tykLogging,
  extractTykHeaders
};
