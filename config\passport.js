const passport = require("passport");
const { Strategy: JwtStrategy, ExtractJwt } = require("passport-jwt");
const SamlStrategy = require("passport-saml").Strategy;
const OpenIDConnectStrategy = require("passport-openidconnect").Strategy;
const { OIDCStrategy: AzureOIDCStrategy } = require("passport-azure-ad");
const config = require("./config");
const { Identity, sequelize } = require("../models");
const { tokenTypes } = require("./attributes");
const tykConfig = require("./tyk");
const logger = require("./logger");
const jwt = require("jsonwebtoken");
const tykRoleMappingService = require("../services/tyk.service");

// ---------------------
// Email & Password Strategy
// ---------------------
const jwtOptions = {
  secretOrKey: config.jwt.secret,
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
};
const jwtVerify = async (payload, done) => {
  try {
    if (payload.type !== tokenTypes.ACCESS) {
      throw new Error("Invalid token type");
    }
    const identity = await Identity.findByPk(payload.sub);
    if (!identity) {
      return done(null, false);
    }

    // Add permissions from JWT payload to identity object for easy access
    identity.permissions = payload.permissions || [];

    done(null, identity);
  } catch (error) {
    done(error, false);
  }
};
passport.use("custom", new JwtStrategy(jwtOptions, jwtVerify));

// ---------------------
// Tyk Authentication Strategy
// ---------------------
const TykStrategy = require("passport-custom").Strategy;

const tykVerify = async (req, done) => {
  try {
    // Check if Tyk is enabled
    if (!tykConfig.isEnabled()) {
      return done(new Error("Tyk reverse proxy is not enabled"), false);
    }

    // Extract Tyk headers for analytics and rate limiting info
    const tykHeaders = {
      apiKey: req.headers['x-tyk-api-key'],
      apiId: req.headers['x-tyk-api-id'],
      orgId: req.headers['x-tyk-org-id'],
      sessionAlias: req.headers['x-tyk-session-alias'],
      rateLimitRemaining: req.headers['x-ratelimit-remaining'],
      rateLimitLimit: req.headers['x-ratelimit-limit'],
      rateLimitReset: req.headers['x-ratelimit-reset'],
      // Tyk can forward original auth headers
      originalAuth: req.headers['x-tyk-original-authorization'] || req.headers['authorization']
    };

    // Log Tyk headers for analytics
    logger.info('Tyk reverse proxy headers received', {
      tykHeaders: {
        apiId: tykHeaders.apiId,
        orgId: tykHeaders.orgId,
        rateLimitRemaining: tykHeaders.rateLimitRemaining,
        rateLimitLimit: tykHeaders.rateLimitLimit
      },
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      path: req.path
    });

    // Use the original authorization header for authentication
    const authHeader = tykHeaders.originalAuth || req.headers['authorization'];

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return done(null, false, { message: 'No valid authorization token provided' });
    }

    // Extract JWT token
    const token = authHeader.replace('Bearer ', '');

    // Verify JWT token using existing logic
    try {
      const payload = jwt.verify(token, config.jwt.secret);

      if (payload.type !== tokenTypes.ACCESS) {
        throw new Error("Invalid token type");
      }

      // Find identity in database
      const identity = await Identity.findByPk(payload.sub, {
        include: [{
          model: sequelize.models.Role,
          as: 'role',
          include: [{
            model: sequelize.models.Permission,
            as: 'permission',
            attributes: ['name'],
            through: { attributes: [] }
          }],
          through: { attributes: [] }
        }]
      });

      if (!identity) {
        return done(null, false, { message: 'Identity not found' });
      }

      // Get dynamic permissions from database relationships
      const permissions = identity.role.flatMap(role =>
        role.permission.map(p => p.name)
      );

      // Add Tyk-specific metadata for analytics
      const tykMetadata = {
        api_id: tykHeaders.apiId,
        org_id: tykHeaders.orgId,
        session_alias: tykHeaders.sessionAlias,
        rate_limit: {
          remaining: tykHeaders.rateLimitRemaining,
          limit: tykHeaders.rateLimitLimit,
          reset: tykHeaders.rateLimitReset
        },
        proxied_via_tyk: true
      };

      // Map Tyk metadata to additional dynamic permissions
      const tykPermissions = await tykRoleMappingService.mapTykMetadataToPermissions(identity, tykMetadata);

      // Combine database permissions with Tyk-derived permissions
      const allPermissions = [...new Set([...permissions, ...tykPermissions])];

      // Add permissions to identity object
      identity.permissions = allPermissions;
      identity.tyk_metadata = tykMetadata;

      // Mark as Tyk-proxied for logging purposes
      identity.auth_method = 'custom_via_tyk';

      logger.info('Authentication successful via Tyk proxy', {
        identityId: identity.identity_id,
        email: identity.email,
        permissions: permissions.length,
        authMethod: identity.auth_method,
        tykApiId: tykHeaders.apiId
      });

      done(null, identity);
    } catch (jwtError) {
      logger.warn('JWT verification failed in Tyk proxy mode', {
        error: jwtError.message,
        tykApiId: tykHeaders.apiId
      });
      return done(null, false, { message: 'Invalid or expired token' });
    }

  } catch (error) {
    logger.error('Tyk proxy authentication error', {
      error: error.message,
      stack: error.stack
    });
    done(error, false);
  }
};

passport.use("tyk", new TykStrategy(tykVerify));

// ---------------------
// SAML Strategy
// ---------------------
passport.use(
  "saml",
  new SamlStrategy(
    {
      path: config.saml.callbackUrl,
      entryPoint: config.saml.entryPoint,
      issuer: config.saml.issuer,
      cert: config.saml.cert,
    },
    async (profile, done) => {
      try {
        // Lookup the user by a unique attribute (e.g. email) from the SAML profile.
        let identity = await Identity.findOne({
          where: { email: profile.email },
        });
        if (!identity) {
          identity = await Identity.create({
            username: profile.nameID,
            email: profile.email,
            first_name: profile.firstName,
            last_name: profile.lastName,
          });
        }
        return done(null, identity);
      } catch (error) {
        return done(error, false);
      }
    }
  )
);

// ---------------------
// OpenID Connect Strategy
// ---------------------
passport.use(
  "oidc",
  new OpenIDConnectStrategy(
    {
      issuer: config.oidc.issuer,
      authorizationURL: config.oidc.authorizationURL,
      tokenURL: config.oidc.tokenURL,
      userInfoURL: config.oidc.userInfoURL,
      clientID: config.oidc.clientID,
      clientSecret: config.oidc.clientSecret,
      callbackURL: config.oidc.callbackURL,
    },
    async (issuer, sub, profile, accessToken, refreshToken, done) => {
      try {
        let identity = await Identity.findOne({
          where: { email: profile.email },
        });
        if (!identity) {
          identity = await Identity.create({
            username: profile.displayName || profile.email,
            email: profile.email,
            first_name: profile.given_name,
            last_name: profile.family_name,
          });
        }
        return done(null, identity);
      } catch (error) {
        return done(error, false);
      }
    }
  )
);

// ---------------------
// Azure AD Strategy (OIDC)
// ---------------------
passport.use(
  "azure",
  new AzureOIDCStrategy(
    {
      identityMetadata: config.azure.identityMetadata,
      clientID: config.azure.clientID,
      responseType: config.azure.responseType,
      responseMode: config.azure.responseMode,
      redirectUrl: config.azure.callbackURL,
      allowHttpForRedirectUrl: config.azure.allowHttpForRedirectUrl,
      clientSecret: config.azure.clientSecret,
    },
    async (iss, sub, profile, accessToken, refreshToken, done) => {
      try {
        // Note: The structure of the profile from Azure AD is found in profile._json.
        let identity = await Identity.findOne({
          where: { email: profile._json.email },
        });
        if (!identity) {
          identity = await Identity.create({
            username: profile.displayName || profile._json.email,
            email: profile._json.email,
            first_name: profile._json.given_name,
            last_name: profile._json.family_name,
          });
        }
        return done(null, identity);
      } catch (error) {
        return done(error, false);
      }
    }
  )
);

module.exports = passport;
