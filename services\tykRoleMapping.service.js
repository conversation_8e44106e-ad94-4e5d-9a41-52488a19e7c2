const { Role, Permission, RolePermission, Identity, IdentityRole } = require('../models');
const logger = require('../config/logger');
const config = require('../config/config');

/**
 * Service for managing dynamic role and permission mapping with Tyk integration
 * This service handles the mapping between Tyk metadata and dynamic roles/permissions
 */

class TykRoleMappingService {
  
  /**
   * Create or update a role dynamically
   * @param {string} roleName - Name of the role
   * @param {string} description - Description of the role
   * @param {Array<string>} permissionNames - Array of permission names
   * @returns {Promise<Object>} - Created or updated role
   */
  async createOrUpdateRole(roleName, description, permissionNames = []) {
    try {
      // Find or create the role
      let [role, created] = await Role.findOrCreate({
        where: { name: roleName },
        defaults: {
          name: roleName,
          description: description || `Dynamic role: ${roleName}`,
          is_active: true
        }
      });

      if (!created && description) {
        // Update description if role exists and description is provided
        role.description = description;
        await role.save();
      }

      // Handle permissions if provided
      if (permissionNames.length > 0) {
        await this.assignPermissionsToRole(role.role_id, permissionNames);
      }

      logger.info('Role created or updated', {
        roleId: role.role_id,
        roleName: role.name,
        created,
        permissionCount: permissionNames.length
      });

      return role;
    } catch (error) {
      logger.error('Error creating or updating role', {
        roleName,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Create or update a permission dynamically
   * @param {string} permissionName - Name of the permission
   * @param {string} description - Description of the permission
   * @returns {Promise<Object>} - Created or updated permission
   */
  async createOrUpdatePermission(permissionName, description) {
    try {
      let [permission, created] = await Permission.findOrCreate({
        where: { name: permissionName },
        defaults: {
          name: permissionName,
          description: description || `Dynamic permission: ${permissionName}`,
          is_active: true
        }
      });

      if (!created && description) {
        // Update description if permission exists and description is provided
        permission.description = description;
        await permission.save();
      }

      logger.info('Permission created or updated', {
        permissionId: permission.permission_id,
        permissionName: permission.name,
        created
      });

      return permission;
    } catch (error) {
      logger.error('Error creating or updating permission', {
        permissionName,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Assign permissions to a role
   * @param {string} roleId - Role ID
   * @param {Array<string>} permissionNames - Array of permission names
   * @returns {Promise<void>}
   */
  async assignPermissionsToRole(roleId, permissionNames) {
    try {
      // Create permissions if they don't exist
      const permissions = await Promise.all(
        permissionNames.map(name => this.createOrUpdatePermission(name))
      );

      // Remove existing role-permission associations
      await RolePermission.destroy({
        where: { role_id: roleId }
      });

      // Create new associations
      const rolePermissions = permissions.map(permission => ({
        role_id: roleId,
        permission_id: permission.permission_id
      }));

      await RolePermission.bulkCreate(rolePermissions);

      logger.info('Permissions assigned to role', {
        roleId,
        permissionCount: permissions.length,
        permissions: permissionNames
      });
    } catch (error) {
      logger.error('Error assigning permissions to role', {
        roleId,
        permissionNames,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Assign roles to an identity
   * @param {string} identityId - Identity ID
   * @param {Array<string>} roleNames - Array of role names
   * @returns {Promise<void>}
   */
  async assignRolesToIdentity(identityId, roleNames) {
    try {
      // Create roles if they don't exist
      const roles = await Promise.all(
        roleNames.map(name => this.createOrUpdateRole(name))
      );

      // Remove existing identity-role associations
      await IdentityRole.destroy({
        where: { identity_id: identityId }
      });

      // Create new associations
      const identityRoles = roles.map(role => ({
        identity_id: identityId,
        role_id: role.role_id
      }));

      await IdentityRole.bulkCreate(identityRoles);

      logger.info('Roles assigned to identity', {
        identityId,
        roleCount: roles.length,
        roles: roleNames
      });
    } catch (error) {
      logger.error('Error assigning roles to identity', {
        identityId,
        roleNames,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get all permissions for an identity through their roles
   * @param {string} identityId - Identity ID
   * @returns {Promise<Array<string>>} - Array of permission names
   */
  async getIdentityPermissions(identityId) {
    try {
      const identity = await Identity.findByPk(identityId, {
        include: [{
          model: Role,
          as: 'role',
          include: [{
            model: Permission,
            as: 'permission',
            attributes: ['name'],
            through: { attributes: [] }
          }],
          through: { attributes: [] }
        }]
      });

      if (!identity) {
        return [];
      }

      const permissions = identity.role.flatMap(role => 
        role.permission.map(p => p.name)
      );

      // Remove duplicates
      return [...new Set(permissions)];
    } catch (error) {
      logger.error('Error getting identity permissions', {
        identityId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Map Tyk metadata to dynamic roles and permissions
   * This is called when a user authenticates via Tyk proxy
   * @param {Object} identity - Identity object
   * @param {Object} tykMetadata - Tyk metadata from headers
   * @returns {Promise<Array<string>>} - Array of permission names
   */
  async mapTykMetadataToPermissions(identity, tykMetadata) {
    try {
      if (!tykMetadata || !config.tyk.enabled) {
        return [];
      }

      // Extract role information from Tyk metadata
      const tykRoles = this.extractRolesFromTykMetadata(tykMetadata);
      
      if (tykRoles.length === 0) {
        logger.info('No Tyk roles found, using default permissions', {
          identityId: identity.identity_id
        });
        return ['view_facilities']; // Default permission
      }

      // Create or update roles based on Tyk metadata
      await Promise.all(tykRoles.map(roleInfo => 
        this.createOrUpdateRole(roleInfo.name, roleInfo.description, roleInfo.permissions)
      ));

      // Assign roles to identity if it exists in database
      if (identity.identity_id && !identity.identity_id.startsWith('tyk_')) {
        await this.assignRolesToIdentity(identity.identity_id, tykRoles.map(r => r.name));
        return await this.getIdentityPermissions(identity.identity_id);
      }

      // For virtual Tyk users, return permissions directly
      const allPermissions = tykRoles.flatMap(role => role.permissions);
      return [...new Set(allPermissions)];

    } catch (error) {
      logger.error('Error mapping Tyk metadata to permissions', {
        identityId: identity.identity_id,
        error: error.message
      });
      // Return default permissions on error
      return ['view_facilities'];
    }
  }

  /**
   * Extract role information from Tyk metadata
   * @param {Object} tykMetadata - Tyk metadata
   * @returns {Array<Object>} - Array of role objects with name, description, and permissions
   */
  extractRolesFromTykMetadata(tykMetadata) {
    const roles = [];

    // Check for API-level roles
    if (tykMetadata.api_id) {
      roles.push({
        name: `tyk_api_${tykMetadata.api_id}`,
        description: `Tyk API access role for ${tykMetadata.api_id}`,
        permissions: ['api_access', 'view_facilities']
      });
    }

    // Check for organization-level roles
    if (tykMetadata.org_id) {
      roles.push({
        name: `tyk_org_${tykMetadata.org_id}`,
        description: `Tyk organization role for ${tykMetadata.org_id}`,
        permissions: ['org_access', 'view_facilities', 'view_appointments']
      });
    }

    // Check for rate limit based roles (premium users get more permissions)
    if (tykMetadata.rate_limit && tykMetadata.rate_limit.limit) {
      const limit = parseInt(tykMetadata.rate_limit.limit, 10);
      if (limit > 1000) {
        roles.push({
          name: 'tyk_premium_user',
          description: 'Premium user with high rate limits',
          permissions: ['premium_access', 'view_facilities', 'view_appointments', 'view_patients']
        });
      } else {
        roles.push({
          name: 'tyk_standard_user',
          description: 'Standard user with normal rate limits',
          permissions: ['standard_access', 'view_facilities']
        });
      }
    }

    return roles;
  }

  /**
   * Sync Tyk analytics data with role usage
   * This can be called periodically to update role usage statistics
   * @returns {Promise<void>}
   */
  async syncTykAnalytics() {
    try {
      if (!config.tyk.enabled) {
        return;
      }

      logger.info('Syncing Tyk analytics with role usage');
      
      // This would integrate with Tyk's analytics API
      // For now, we'll just log the sync attempt
      
      logger.info('Tyk analytics sync completed');
    } catch (error) {
      logger.error('Error syncing Tyk analytics', {
        error: error.message
      });
    }
  }
}

module.exports = new TykRoleMappingService();
