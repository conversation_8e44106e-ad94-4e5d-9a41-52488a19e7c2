# Tyk API Gateway Reverse Proxy Integration

This document describes the complete Tyk API Gateway reverse proxy integration for the CareMate API. The integration uses Tyk as a reverse proxy for rate limiting and analytics while preserving your existing dynamic authentication system with runtime role and permission creation.

## 🎯 Overview

The Tyk reverse proxy integration provides:
- **Reverse Proxy Mode**: Tyk handles rate limiting and analytics, forwards requests with original auth
- **Dynamic Role System**: Preserves existing runtime role and permission creation
- **SSO Reverse Proxy**: SAML, OIDC, and Azure AD authentication through Tyk
- **Smart Rate Limiting**: Tyk handles rate limiting, local system logs and monitors
- **Comprehensive Analytics**: Tyk-specific request/response logging and analytics
- **App Initialization**: Automatic Tyk connection check on startup
- **Full Backward Compatibility**: All existing functionality preserved

## 🚀 Quick Setup (5 minutes)

### Step 1: Your Tyk Gateway is Running
✅ **Confirmed working at**: `http://localhost:8181/hello`

### Step 2: Configure Environment Variables
```bash
# Copy example configuration
cp .env.tyk.example .env

# Key settings for Tyk reverse proxy mode
AUTH_MODE=tyk
TYK_ENABLED=true
TYK_GATEWAY_URL=http://localhost:8181
TYK_GATEWAY_SECRET=your-tyk-secret
TYK_API_ID=caremate-api
TYK_ORG_ID=your-org-id
TYK_TARGET_URL=http://localhost:3000
```

### Step 3: Start Your Application
```bash
npm start
```

The app will automatically check Tyk connection on startup and log the results.

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```bash
# Authentication Mode
AUTH_MODE=tyk  # or 'custom' for direct access

# Tyk Configuration
TYK_ENABLED=true
TYK_GATEWAY_URL=http://localhost:8080
TYK_GATEWAY_SECRET=your-tyk-secret
TYK_API_ID=caremate-api-v1
TYK_ORG_ID=your-org-id
TYK_LISTEN_PATH=/api/v1
TYK_TARGET_URL=http://localhost:3000

# Rate Limiting
TYK_RATE_LIMIT_ENABLED=true
TYK_RATE_LIMIT_PER=60
TYK_RATE_LIMIT_RATE=1000

# Quota (Optional)
TYK_QUOTA_ENABLED=false
TYK_QUOTA_MAX=10000
TYK_QUOTA_RENEWAL_RATE=3600
```

### Configuration Files

1. **config/config.js** - Main configuration with Tyk settings
2. **config/tyk.js** - Tyk-specific utilities and API client
3. **config/passport.js** - Enhanced with Tyk authentication strategy

## Authentication Modes

### Custom Mode (Default)
- Direct access to your API
- Uses existing JWT-based authentication
- Validates tokens against your database with dynamic roles
- Supports all existing SSO providers (SAML, OIDC, Azure AD)

### Tyk Proxy Mode
- Requests go through Tyk reverse proxy first
- Tyk handles rate limiting and analytics
- Original JWT authentication is preserved and forwarded
- Dynamic roles and permissions work exactly as before
- Additional Tyk-based roles can be created dynamically
- **SSO Integration**: SAML, OIDC, and Azure AD routes through Tyk
- **App Initialization**: Automatic Tyk connection check on startup

## 🔐 SSO Reverse Proxy Integration

### SSO Flow Through Tyk

When `AUTH_MODE=tyk` is enabled, SSO authentication flows through Tyk:

1. **SSO Login Request** → Tyk Gateway → Your API
2. **Tyk Gateway** applies rate limiting and analytics
3. **Your API** initiates SSO with provider (SAML/OIDC/Azure AD)
4. **SSO Provider** redirects back to Tyk callback URL
5. **Tyk Gateway** forwards callback to your API
6. **Your API** processes SSO authentication + creates dynamic roles
7. **Tyk metadata** enhances user with additional roles

### SSO Callback URLs

The system automatically generates callback URLs based on mode:

```javascript
// In tyk mode:
// SAML: http://localhost:8181/api/v1/auth/saml/callback
// OIDC: http://localhost:8181/api/v1/auth/oidc/callback
// Azure: http://localhost:8181/api/v1/auth/azure/callback

// In custom mode:
// SAML: http://localhost:3000/auth/saml/callback
// OIDC: http://localhost:3000/auth/oidc/callback
// Azure: http://localhost:3000/auth/azure/callback
```

### SSO + Dynamic Roles

When SSO authentication comes through Tyk, the system:
1. **Processes SSO authentication** (existing logic)
2. **Creates/assigns dynamic roles** (existing logic)
3. **Adds Tyk-specific roles** like `sso_saml_user`, `tyk_sso_user`
4. **Combines permissions** from database + Tyk metadata
5. **Logs comprehensive analytics** for both SSO and Tyk

## 🚀 App Initialization

The application automatically checks Tyk connection on startup:

```javascript
// In server.js - called after database connection
await app.initializeTyk();
```

**Startup Logs:**
```
[2025-07-02 18:40:48] info: Connected to PostgreSQL
[2025-07-02 18:40:48] info: Initializing Tyk connection
[2025-07-02 18:40:48] info: Tyk initialization successful
[2025-07-02 18:40:48] info: Listening to port 3000
```

If Tyk is unavailable, the app continues without Tyk integration.

## Implementation Details

### Files Modified/Added

#### Core Configuration
- `config/config.js` - Added Tyk + SSO configuration with dynamic callback URLs
- `config/tyk.js` - **NEW** - Tyk utilities, API client, and initialization
- `config/passport.js` - Enhanced Tyk strategy with dynamic role mapping
- `server.js` - Added Tyk initialization on app startup

#### Services
- `services/tyk.service.js` - **NEW** - Dynamic role mapping with Tyk metadata

#### Middleware
- `middlewares/auth.js` - Enhanced dual mode support (custom/tyk)
- `middlewares/rateLimiter.js` - Smart rate limiting with Tyk integration
- `middlewares/tykLogger.js` - **NEW** - Tyk analytics and logging
- `middlewares/ssoTykProxy.js` - **NEW** - SSO reverse proxy through Tyk

#### Routes
- `routes/auth.route.js` - Enhanced SSO routes with Tyk proxy middleware

#### Application
- `app.js` - Integrated Tyk logging and initialization

#### Testing & Documentation
- `tests/tyk-integration.test.js` - **NEW** - Comprehensive tests
- `.env.tyk.example` - **NEW** - Example configuration with correct Tyk URL
- `docs/TYK_INTEGRATION.md` - **NEW** - Complete integration guide

### Authentication Flow

#### Custom Mode Flow
1. Extract JWT from Authorization header
2. Validate JWT signature and expiration
3. Load user from database
4. Check permissions against database

#### Tyk Mode Flow
1. Extract Tyk headers (x-tyk-*)
2. Validate API key with Tyk (optional)
3. Create virtual user from Tyk headers
4. Map Tyk roles to application permissions

### Tyk Headers Supported

The system recognizes these Tyk headers:
- `x-tyk-api-key` - API key used for authentication
- `x-tyk-user-email` - User email from Tyk
- `x-tyk-user-roles` - Comma-separated user roles
- `x-tyk-user-id` - User ID from Tyk
- `x-ratelimit-remaining` - Remaining rate limit
- `x-ratelimit-limit` - Rate limit maximum
- `x-ratelimit-reset` - Rate limit reset time

## Usage Examples

### Basic Route Protection
```javascript
// Existing code works unchanged
router.get('/facilities', auth('view_facilities'), FacilityController.list);
```

### Force Custom Authentication
```javascript
const { authCustom } = require('../middlewares/auth');
router.get('/internal', authCustom('admin_access'), InternalController.data);
```

### Force Tyk Authentication
```javascript
const { authTyk } = require('../middlewares/auth');
router.get('/tyk-only', authTyk('view_data'), TykController.data);
```

### Optional Authentication
```javascript
const { authOptional } = require('../middlewares/auth');
router.get('/public', authOptional(), PublicController.data);
```

### Custom Rate Limiting
```javascript
const { forceLocalRateLimit, hybridRateLimit } = require('../middlewares/rateLimiter');

// Always use local rate limiting
router.use('/sensitive', forceLocalRateLimit({ max: 10 }));

// Use Tyk limits if available, otherwise local
router.use('/api', hybridRateLimit({ max: 100 }));
```

## Role Mapping

Tyk roles are mapped to application permissions:

- `admin` → All permissions
- `user` → Basic view permissions
- `readonly` → View-only permissions
- Default → Minimal permissions

Customize mapping in `config/passport.js`:

```javascript
switch (role.toLowerCase()) {
  case 'admin':
    permissions.push(...require('./permissions'));
    break;
  case 'custom_role':
    permissions.push('custom_permission');
    break;
}
```

## Logging

### Tyk-Specific Logs
- Request/Response logging with Tyk headers
- Authentication events
- Rate limit monitoring
- Error tracking with Tyk context

### Log Levels
- `INFO` - Normal operations
- `WARN` - Rate limit warnings, auth failures
- `ERROR` - Authentication errors, Tyk API failures
- `DEBUG` - Detailed header information

## Monitoring

### Health Checks
```javascript
const tykConfig = require('./config/tyk');
const health = await tykConfig.getHealth();
```

### Rate Limit Monitoring
The system automatically logs warnings when rate limits are low (< 10% remaining).

## Troubleshooting

### Common Issues

1. **Tyk headers not received**
   - Check Tyk API definition configuration
   - Verify custom middleware is enabled in Tyk
   - Ensure headers are being forwarded

2. **Authentication failing**
   - Check `AUTH_MODE` environment variable
   - Verify Tyk configuration parameters
   - Check logs for detailed error messages

3. **Rate limiting not working**
   - Verify `TYK_RATE_LIMIT_ENABLED` setting
   - Check if Tyk headers are present
   - Review rate limit configuration in Tyk

### Debug Mode
Enable debug logging:
```bash
LOG_LEVEL=debug
```

## Migration Guide

### From Custom to Tyk
1. Set up Tyk Gateway
2. Configure Tyk API definition
3. Update environment variables
4. Test with `AUTH_MODE=tyk`
5. Monitor logs for issues

### From Tyk to Custom
1. Set `AUTH_MODE=custom`
2. Ensure JWT configuration is correct
3. Test authentication flows
4. Update any Tyk-specific code

## Security Considerations

1. **API Key Validation**: Optional validation with Tyk API
2. **Header Validation**: Tyk headers are trusted when present
3. **Fallback Security**: Local rate limiting as backup
4. **Logging**: Sensitive data is not logged

## Performance Impact

- **Tyk Mode**: Minimal overhead, mostly header processing
- **Custom Mode**: No change from existing implementation
- **Hybrid Rate Limiting**: Negligible overhead
- **Logging**: Configurable, can be disabled

## Implementation Coverage

### ✅ Fully Implemented with Tyk Support

1. **Authentication & Authorization**
   - All route endpoints support both custom and Tyk auth
   - Automatic strategy selection based on configuration
   - Role-based permission mapping
   - Virtual user creation for Tyk users

2. **Rate Limiting**
   - Smart rate limiting with Tyk header detection
   - Fallback to local rate limiting
   - Hybrid mode support
   - Rate limit monitoring and warnings

3. **Logging**
   - Comprehensive Tyk request/response logging
   - Authentication event logging
   - Error logging with Tyk context
   - Rate limit monitoring logs

4. **Configuration Management**
   - Environment-based configuration
   - Validation and health checks
   - Tyk API client integration
   - Backward compatibility

### ⚠️ Partially Implemented

1. **Database Integration**
   - Tyk users are virtual (not stored in database)
   - Permission mapping is code-based, not database-driven
   - Activity logging works but doesn't persist Tyk user data

2. **SSO Integration**
   - SAML, OIDC, and Azure AD work in custom mode only
   - Tyk mode relies on Tyk's authentication mechanisms
   - No direct SSO integration in Tyk mode

### ❌ Not Implemented with Tyk

1. **User Management Endpoints**
   - `/identity` routes still use custom authentication only
   - User creation/modification requires custom mode
   - Profile management not integrated with Tyk users

2. **Admin Functions**
   - System administration endpoints
   - Cache management
   - Database operations

3. **File Upload/Download**
   - Document upload endpoints
   - Image processing
   - File serving

4. **Real-time Features**
   - WebSocket connections
   - Server-sent events
   - Real-time notifications

### 🔧 Areas for Future Enhancement

1. **Advanced Tyk Integration**
   - Tyk analytics integration
   - Custom Tyk plugins
   - Advanced quota management
   - Multi-organization support

2. **Database Integration**
   - Persist Tyk user data
   - Database-driven role mapping
   - Audit trail for Tyk users

3. **Monitoring & Observability**
   - Tyk metrics collection
   - Performance monitoring
   - Health check endpoints
   - Circuit breaker patterns

## Recommendation for Tyk Usage

### Use Tyk Mode When:
- You have Tyk Gateway already deployed
- You need centralized API management
- You want to leverage Tyk's advanced features
- You're building new APIs from scratch

### Use Custom Mode When:
- You need full user management capabilities
- You require complex permission systems
- You're migrating existing applications
- You need SSO integration beyond what Tyk provides

### Hybrid Approach:
- Use Tyk for external APIs
- Use custom auth for admin/internal APIs
- Leverage both systems based on endpoint requirements

## Future Enhancements

1. **Advanced Role Mapping**: Database-driven role mapping
2. **Tyk Analytics Integration**: Enhanced metrics collection
3. **Multi-Tenant Support**: Organization-based routing
4. **Circuit Breaker**: Automatic fallback on Tyk failures
5. **User Persistence**: Store Tyk users in database
6. **Advanced Monitoring**: Comprehensive observability
