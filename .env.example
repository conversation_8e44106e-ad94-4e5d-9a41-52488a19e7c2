# Environment Configuration
NODE_ENV=development
PORT=3000
SERVER_URL=http://localhost:3000

# Database Configuration
DB_DIALECT=postgres
DB_WRITE_HOST=localhost
DB_WRITE_USERNAME=postgres
DB_WRITE_PASSWORD=password
DB_WRITE_DATABASE=caremate_dev
DB_READ_HOST=localhost
DB_READ_USERNAME=postgres
DB_READ_PASSWORD=password
DB_READ_DATABASE=caremate_dev
DB_LOGGING=false

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_ACCESS_EXPIRATION_MINUTES=30
JWT_REFRESH_EXPIRATION_DAYS=30

# Authentication Mode Configuration
# AUTH_MODE can be 'custom' (default) or 'tyk'
# - custom: Uses built-in authentication system
# - tyk: Uses Tyk as reverse proxy for rate limiting, analytics, and SSO
AUTH_MODE=custom

# Tyk Configuration (Required when AUTH_MODE=tyk)
# Enable/disable Tyk integration
TYK_ENABLED=false

# Tyk Gateway Configuration
TYK_GATEWAY_URL=http://localhost:8080
TYK_GATEWAY_SECRET=your-tyk-gateway-secret
TYK_API_ID=caremate-api
TYK_ORG_ID=your-org-id
TYK_LISTEN_PATH=/api/v1/
TYK_TARGET_URL=http://localhost:3000

# Tyk Rate Limiting Configuration
TYK_RATE_LIMIT_ENABLED=true
TYK_RATE_LIMIT_PER=60
TYK_RATE_LIMIT_RATE=1000

# Tyk Quota Configuration
TYK_QUOTA_ENABLED=false
TYK_QUOTA_MAX=10000
TYK_QUOTA_RENEWAL_RATE=3600

# Logging Configuration
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=30d
LOG_FILE_TRANSPORT=false

# Message Queuing Configuration
MESSAGE_QUEUING=true
RABBITMQ_URL=amqp://localhost:5672
CONCURRENCY_LIMIT=15
COLLECTION_TIMEOUT=3000
SHUTDOWN_TIMEOUT=30000

# SSO Configuration
# SAML Configuration
SAML_ENTRY_POINT=https://your-idp.com/saml/sso
SAML_ISSUER=caremate-app
SAML_CALLBACK_URL=http://localhost:3000/auth/saml/callback
SAML_CERT=-----BEGIN CERTIFICATE-----...-----END CERTIFICATE-----

# OpenID Connect Configuration
OIDC_ISSUER=https://your-oidc-provider.com
OIDC_CLIENT_ID=your-oidc-client-id
OIDC_CLIENT_SECRET=your-oidc-client-secret
OIDC_CALLBACK_URL=http://localhost:3000/auth/oidc/callback

# Azure AD Configuration
AZURE_TENANT_ID=your-azure-tenant-id
AZURE_CLIENT_ID=your-azure-client-id
AZURE_CLIENT_SECRET=your-azure-client-secret
AZURE_CALLBACK_URL=http://localhost:3000/auth/azure/callback

# Google SSO Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback

# GitHub SSO Configuration
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GITHUB_CALLBACK_URL=http://localhost:3000/auth/github/callback

# Additional Configuration
REDIS_URL=redis://localhost:6379
MEMCACHED_URL=localhost:11211
