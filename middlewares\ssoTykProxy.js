const config = require('../config/config');
const logger = require('../config/logger');

/**
 * SSO Tyk Proxy Middleware
 * Handles SSO authentication requests when they come through Tyk reverse proxy
 */

/**
 * Middleware to handle SSO requests through Tyk proxy
 * Logs SSO authentication attempts and Tyk metadata
 */
const ssoTykProxyLogger = (provider) => {
  return (req, res, next) => {
    // Only log when Tyk is enabled and in proxy mode
    if (config.auth.mode === 'tyk' && config.tyk.enabled) {
      
      // Extract Tyk headers
      const tykHeaders = {
        apiId: req.headers['x-tyk-api-id'],
        orgId: req.headers['x-tyk-org-id'],
        sessionAlias: req.headers['x-tyk-session-alias'],
        rateLimitRemaining: req.headers['x-ratelimit-remaining'],
        rateLimitLimit: req.headers['x-ratelimit-limit'],
        rateLimitReset: req.headers['x-ratelimit-reset']
      };

      logger.info(`SSO ${provider.toUpperCase()} request via Tyk proxy`, {
        provider,
        tykHeaders,
        userAgent: req.headers['user-agent'],
        ip: req.ip,
        path: req.path,
        method: req.method,
        query: req.query,
        timestamp: new Date().toISOString()
      });

      // Add Tyk metadata to request for later use
      req.tykMetadata = {
        provider,
        proxied: true,
        headers: tykHeaders,
        timestamp: new Date().toISOString()
      };
    } else {
      // Direct SSO request (not through Tyk)
      logger.info(`Direct SSO ${provider.toUpperCase()} request`, {
        provider,
        userAgent: req.headers['user-agent'],
        ip: req.ip,
        path: req.path,
        method: req.method,
        timestamp: new Date().toISOString()
      });

      req.tykMetadata = {
        provider,
        proxied: false,
        timestamp: new Date().toISOString()
      };
    }

    next();
  };
};

/**
 * Middleware to handle SSO callback responses through Tyk proxy
 * Enhances the user object with Tyk metadata for role mapping
 */
const ssoTykCallbackHandler = (provider) => {
  return (req, res, next) => {
    // Store original res.redirect to intercept redirects
    const originalRedirect = res.redirect;
    
    res.redirect = function(url) {
      // Log SSO callback completion
      if (config.auth.mode === 'tyk' && config.tyk.enabled) {
        logger.info(`SSO ${provider.toUpperCase()} callback completed via Tyk proxy`, {
          provider,
          success: !!req.user,
          userId: req.user?.identity_id,
          email: req.user?.email,
          tykMetadata: req.tykMetadata,
          redirectUrl: url,
          timestamp: new Date().toISOString()
        });

        // Add Tyk metadata to user object for role mapping
        if (req.user && req.tykMetadata) {
          req.user.sso_tyk_metadata = {
            provider,
            proxied_via_tyk: true,
            tyk_headers: req.tykMetadata.headers,
            authenticated_at: new Date().toISOString()
          };
        }
      } else {
        logger.info(`Direct SSO ${provider.toUpperCase()} callback completed`, {
          provider,
          success: !!req.user,
          userId: req.user?.identity_id,
          email: req.user?.email,
          redirectUrl: url,
          timestamp: new Date().toISOString()
        });
      }

      // Call original redirect
      originalRedirect.call(this, url);
    };

    next();
  };
};

/**
 * Middleware to enhance SSO user with dynamic roles based on Tyk metadata
 */
const ssoTykRoleEnhancer = (provider) => {
  return async (req, res, next) => {
    if (req.user && req.tykMetadata && req.tykMetadata.proxied) {
      try {
        const tykRoleMappingService = require('../services/tyk.service');
        
        // Create SSO-specific Tyk metadata for role mapping
        const ssoTykMetadata = {
          ...req.tykMetadata.headers,
          sso_provider: provider,
          authentication_method: `${provider}_via_tyk`,
          authenticated_at: new Date().toISOString()
        };

        // Map Tyk metadata to additional permissions
        const tykPermissions = await tykRoleMappingService.mapTykMetadataToPermissions(
          req.user, 
          ssoTykMetadata
        );

        // Add SSO + Tyk specific roles
        const ssoTykRoles = [
          `sso_${provider}_user`,
          `tyk_sso_user`
        ];

        // Create dynamic roles for SSO via Tyk
        await Promise.all(ssoTykRoles.map(roleName => 
          tykRoleMappingService.createOrUpdateRole(
            roleName,
            `SSO ${provider.toUpperCase()} user authenticated via Tyk proxy`,
            tykPermissions
          )
        ));

        // Assign roles to user if they exist in database
        if (req.user.identity_id && !req.user.identity_id.startsWith('tyk_')) {
          await tykRoleMappingService.assignRolesToIdentity(
            req.user.identity_id, 
            ssoTykRoles
          );
        }

        logger.info(`SSO user enhanced with Tyk-based roles`, {
          provider,
          userId: req.user.identity_id,
          email: req.user.email,
          tykRoles: ssoTykRoles,
          tykPermissions: tykPermissions.length,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error(`Error enhancing SSO user with Tyk roles`, {
          provider,
          userId: req.user?.identity_id,
          error: error.message,
          stack: error.stack
        });
        // Continue without enhancement on error
      }
    }

    next();
  };
};

/**
 * Complete SSO Tyk proxy middleware chain
 * Combines logging, callback handling, and role enhancement
 */
const ssoTykProxy = (provider) => {
  return [
    ssoTykProxyLogger(provider),
    ssoTykCallbackHandler(provider),
    ssoTykRoleEnhancer(provider)
  ];
};

/**
 * Middleware to check if SSO should route through Tyk
 * Redirects to Tyk URL if proxy mode is enabled
 */
const ssoTykRedirectCheck = (provider) => {
  return (req, res, next) => {
    // If we're in Tyk proxy mode and this is an initial SSO request (not callback)
    if (config.auth.mode === 'tyk' && 
        config.tyk.enabled && 
        !req.path.includes('/callback') &&
        req.headers.host !== new URL(config.tyk.gatewayUrl).host) {
      
      // Construct Tyk proxy URL
      const tykUrl = `${config.tyk.gatewayUrl}${config.tyk.listenPath}/auth/${provider}/login`;
      
      logger.info(`Redirecting SSO ${provider.toUpperCase()} request to Tyk proxy`, {
        provider,
        originalUrl: req.originalUrl,
        tykUrl,
        timestamp: new Date().toISOString()
      });

      return res.redirect(tykUrl);
    }

    next();
  };
};

module.exports = {
  ssoTykProxyLogger,
  ssoTykCallbackHandler,
  ssoTykRoleEnhancer,
  ssoTykProxy,
  ssoTykRedirectCheck
};
